"use client"

import { useAppStore } from "@/lib/store"

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  return (
    <div className={`animate-spin ${sizeClasses[size]} ${className}`}>
      <svg className="w-full h-full" fill="none" viewBox="0 0 24 24">
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  )
}

interface BackgroundLoadingIndicatorProps {
  show: boolean
  message?: string
}

export function BackgroundLoadingIndicator({ show, message = "Syncing..." }: BackgroundLoadingIndicatorProps) {
  if (!show) return null

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-40">
      <div className="bg-white border border-gray-200 rounded-lg shadow-lg px-4 py-2 flex items-center space-x-2">
        <LoadingSpinner size="sm" className="text-blue-500" />
        <span className="text-sm text-gray-600">{message}</span>
      </div>
    </div>
  )
}

export function GlobalLoadingIndicator() {
  const { pendingOperations } = useAppStore()
  const hasPendingOperations = pendingOperations.size > 0

  return (
    <BackgroundLoadingIndicator 
      show={hasPendingOperations}
      message={`Syncing ${pendingOperations.size} change${pendingOperations.size !== 1 ? 's' : ''}...`}
    />
  )
}

interface OptimisticIndicatorProps {
  isOptimistic?: boolean
  isPending?: boolean
  className?: string
}

export function OptimisticIndicator({ isOptimistic, isPending, className = '' }: OptimisticIndicatorProps) {
  if (!isOptimistic && !isPending) return null

  return (
    <div className={`absolute top-1 right-1 ${className}`}>
      {isPending ? (
        <LoadingSpinner size="sm" className="text-blue-400" />
      ) : isOptimistic ? (
        <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" title="Pending sync" />
      ) : null}
    </div>
  )
}
