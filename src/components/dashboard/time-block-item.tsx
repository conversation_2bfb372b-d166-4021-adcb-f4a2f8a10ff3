"use client"

import { useState } from "react"
import { format } from "date-fns"
import { useAppStore, TimeBlock } from "@/lib/store"
import { useTimeBlocks } from "@/hooks/use-timeblocks"
import { Button } from "@/components/ui/button"
import { TimeBlockForm } from "./time-block-form"

interface TimeBlockItemProps {
  timeBlock: TimeBlock
}

export function TimeBlockItem({ timeBlock }: TimeBlockItemProps) {
  const { deleteTimeBlock } = useTimeBlocks()
  const [showEditForm, setShowEditForm] = useState(false)

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation()
    if (confirm("Are you sure you want to delete this time block?")) {
      try {
        await deleteTimeBlock(timeBlock.id)
      } catch (error) {
        console.error("Error deleting time block:", error)
      }
    }
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowEditForm(true)
  }

  const duration = new Date(timeBlock.endTime).getTime() - new Date(timeBlock.startTime).getTime()
  const durationMinutes = Math.round(duration / (1000 * 60))

  const isFullDay = timeBlock.isFullDay
  const baseClasses = "border rounded p-1.5 mb-0.5 text-xs group transition-colors"
  const fullDayClasses = "bg-purple-100 border-purple-200 hover:bg-purple-200"
  const regularClasses = "bg-blue-100 border-blue-200 hover:bg-blue-200"

  return (
    <>
      <div className={`${baseClasses} ${isFullDay ? fullDayClasses : regularClasses}`}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className={`font-medium truncate ${isFullDay ? 'text-purple-900' : 'text-blue-900'}`}>
              {timeBlock.title}
            </div>
            {!timeBlock.isFullDay && (
              <>
                <div className="text-blue-700 mt-0.5">
                  {format(new Date(timeBlock.startTime), "HH:mm")} - {format(new Date(timeBlock.endTime), "HH:mm")}
                </div>
                <div className="text-blue-600 text-xs">
                  {durationMinutes} min
                </div>
              </>
            )}
            {timeBlock.isFullDay && (
              <div className="text-purple-700 mt-0.5 text-xs">
                All day
              </div>
            )}
            {timeBlock.task && (
              <div className={`text-xs mt-1 truncate ${isFullDay ? 'text-purple-600' : 'text-blue-600'}`}>
                Task: {timeBlock.task.title}
              </div>
            )}
          </div>
          <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleEdit}
              className={`h-6 w-6 ${isFullDay ? 'text-purple-700 hover:text-purple-900' : 'text-blue-700 hover:text-blue-900'}`}
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleDelete}
              className="h-6 w-6 text-red-600 hover:text-red-700"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </Button>
          </div>
        </div>
      </div>

      {/* Edit Form Modal */}
      {showEditForm && (
        <TimeBlockForm
          timeBlock={timeBlock}
          onClose={() => setShowEditForm(false)}
          onSubmit={() => setShowEditForm(false)}
        />
      )}
    </>
  )
}
