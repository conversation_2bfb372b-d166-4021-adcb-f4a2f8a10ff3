"use client"

import { useState } from "react"
import { useAppStore, Task } from "@/lib/store"
import { useTasks } from "@/hooks/use-tasks"
import { Button } from "@/components/ui/button"
import { TaskItem } from "./task-item"

export function TaskList() {
  const { tasks } = useTasks()
  const [filter, setFilter] = useState<"all" | "pending" | "in_progress" | "completed">("all")
  const [searchTerm, setSearchTerm] = useState("")

  const filteredTasks = tasks.filter((task) => {
    const matchesFilter = filter === "all" || task.status.toLowerCase() === filter.replace("_", "_")
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description?.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesFilter && matchesSearch
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-100 text-yellow-800"
      case "IN_PROGRESS":
        return "bg-blue-100 text-blue-800"
      case "COMPLETED":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="p-3 space-y-3">
      {/* Search */}
      <div>
        <input
          type="text"
          placeholder="Search tasks..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Filter Buttons */}
      <div className="flex flex-wrap gap-2">
        {["all", "pending", "in_progress", "completed"].map((filterOption) => (
          <Button
            key={filterOption}
            variant={filter === filterOption ? "default" : "outline"}
            size="sm"
            onClick={() => setFilter(filterOption as any)}
            className="text-xs"
          >
            {filterOption.replace("_", " ").toUpperCase()}
          </Button>
        ))}
      </div>

      {/* Task Count */}
      <div className="text-sm text-gray-600">
        {filteredTasks.length} task{filteredTasks.length !== 1 ? "s" : ""}
      </div>

      {/* Task List */}
      <div className="space-y-1.5">
        {filteredTasks.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {tasks.length === 0 ? (
              <div>
                <svg
                  className="w-12 h-12 mx-auto mb-4 text-gray-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                <p>No tasks yet</p>
                <p className="text-xs">Create your first task to get started</p>
              </div>
            ) : (
              <p>No tasks match your search</p>
            )}
          </div>
        ) : (
          filteredTasks.map((task) => (
            <TaskItem key={task.id} task={task} />
          ))
        )}
      </div>
    </div>
  )
}
