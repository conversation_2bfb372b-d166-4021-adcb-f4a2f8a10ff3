"use client"

import { useState } from "react"
import { format, startOfWeek, addDays, addWeeks, subWeeks, isSameDay } from "date-fns"
import { useAppStore } from "@/lib/store"
import { useTimeBlocks } from "@/hooks/use-timeblocks"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { TimeBlockForm } from "./time-block-form"
import { TimeBlockItem } from "./time-block-item"

export function CalendarView() {
  const { selectedDate, setSelectedDate } = useAppStore()
  const { timeBlocks } = useTimeBlocks()
  const [showTimeBlockForm, setShowTimeBlockForm] = useState(false)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<{ date: Date; hour: number } | null>(null)

  const weekStart = startOfWeek(selectedDate, { weekStartsOn: 0 }) // Sunday start
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i))
  const hours = Array.from({ length: 24 }, (_, i) => i)

  const handlePreviousWeek = () => {
    setSelectedDate(subWeeks(selectedDate, 1))
  }

  const handleNextWeek = () => {
    setSelectedDate(addWeeks(selectedDate, 1))
  }

  const handleTimeSlotClick = (date: Date, hour: number) => {
    setSelectedTimeSlot({ date, hour })
    setShowTimeBlockForm(true)
  }

  const getTimeBlocksForSlot = (date: Date, hour: number) => {
    return timeBlocks.filter((block) => {
      const blockStart = new Date(block.startTime)
      const blockHour = blockStart.getHours()
      return !block.isFullDay && isSameDay(blockStart, date) && blockHour === hour
    })
  }

  const getFullDayEventsForDate = (date: Date) => {
    return timeBlocks.filter((block) => {
      return block.isFullDay && isSameDay(new Date(block.startTime), date)
    })
  }

  const handleFullDayClick = (date: Date) => {
    setSelectedTimeSlot({ date, hour: 0 })
    setShowTimeBlockForm(true)
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Calendar Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Week of {format(weekStart, "MMM d, yyyy")}
          </h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handlePreviousWeek}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Button>
            <Button variant="outline" size="sm" onClick={handleNextWeek}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Button>
          </div>
        </div>
        <Button
          onClick={() => setSelectedDate(new Date())}
          variant="outline"
          size="sm"
        >
          Today
        </Button>
      </div>

      {/* Calendar Grid */}
      <div className="overflow-x-auto">
        <div className="min-w-[800px]">
          {/* Day Headers */}
          <div className="grid grid-cols-8 border-b border-gray-200">
            <div className="p-3 text-sm font-medium text-gray-500">Time</div>
            {weekDays.map((day) => (
              <div key={day.toISOString()} className="p-3 text-center border-l border-gray-200">
                <div className="text-sm font-medium text-gray-900">
                  {format(day, "EEE")}
                </div>
                <div className={`text-lg font-semibold mt-1 ${
                  isSameDay(day, new Date()) ? "text-blue-600" : "text-gray-700"
                }`}>
                  {format(day, "d")}
                </div>
              </div>
            ))}
          </div>

          {/* Full Day Events Section */}
          <div className="grid grid-cols-8 border-b-2 border-gray-300 bg-gray-50">
            <div className="p-2 text-xs font-medium text-gray-500 border-r border-gray-200 flex items-center">
              All Day
            </div>
            {weekDays.map((day) => {
              const fullDayEvents = getFullDayEventsForDate(day)
              return (
                <div
                  key={`fullday-${day.toISOString()}`}
                  className="p-1.5 border-l border-gray-200 min-h-[60px] cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleFullDayClick(day)}
                >
                  <div className="space-y-0.5">
                    {fullDayEvents.map((block) => (
                      <TimeBlockItem key={block.id} timeBlock={block} />
                    ))}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Time Slots */}
          <div className="max-h-[600px] overflow-y-auto">
            {hours.map((hour) => (
              <div key={hour} className="grid grid-cols-8 border-b border-gray-100 hover:bg-gray-50">
                <div className="p-3 text-sm text-gray-500 border-r border-gray-200">
                  {format(new Date().setHours(hour, 0, 0, 0), "HH:mm")}
                </div>
                {weekDays.map((day) => {
                  const blocksInSlot = getTimeBlocksForSlot(day, hour)
                  return (
                    <div
                      key={`${day.toISOString()}-${hour}`}
                      className="p-1.5 border-l border-gray-200 min-h-[48px] cursor-pointer hover:bg-blue-50 transition-colors"
                      onClick={() => handleTimeSlotClick(day, hour)}
                    >
                      {blocksInSlot.map((block) => (
                        <TimeBlockItem key={block.id} timeBlock={block} />
                      ))}
                    </div>
                  )
                })}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Time Block Form Modal */}
      {showTimeBlockForm && (
        <TimeBlockForm
          selectedTimeSlot={selectedTimeSlot}
          onClose={() => {
            setShowTimeBlockForm(false)
            setSelectedTimeSlot(null)
          }}
          onSubmit={() => {
            setShowTimeBlockForm(false)
            setSelectedTimeSlot(null)
          }}
        />
      )}
    </div>
  )
}
