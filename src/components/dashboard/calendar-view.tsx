"use client"

import { useState } from "react"
import { format, startOfWeek, addDays, addWeeks, subWeeks, isSameDay, addHours } from "date-fns"
import {
  DndContext,
  DragOverlay,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
  DragEndEvent,
  DragStartEvent,
  closestCenter,
  useDroppable
} from "@dnd-kit/core"
import { useAppStore } from "@/lib/store"
import { useTimeBlocks } from "@/hooks/use-timeblocks"
import { Button } from "@/components/ui/button"
import { TimeBlockForm } from "./time-block-form"
import { TimeBlockItem } from "./time-block-item"

// Droppable time slot component
function DroppableTimeSlot({
  date,
  hour,
  children,
  onClick
}: {
  date: Date
  hour: number
  children: React.ReactNode
  onClick: () => void
}) {
  const { isOver, setNodeRef } = useDroppable({
    id: `timeslot-${date.toISOString()}-${hour}`,
    data: {
      type: 'timeslot',
      date,
      hour,
    },
  })

  return (
    <div
      ref={setNodeRef}
      className={`calendar-time-slot calendar-drop-zone border-l border-gray-200 cursor-pointer hover:bg-blue-50 transition-colors ${
        isOver ? 'drag-over' : ''
      }`}
      onClick={onClick}
    >
      <div className="calendar-time-slot-content">
        {children}
      </div>
    </div>
  )
}

export function CalendarView() {
  const { selectedDate, setSelectedDate } = useAppStore()
  const { timeBlocks, updateTimeBlock } = useTimeBlocks()
  const [showTimeBlockForm, setShowTimeBlockForm] = useState(false)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<{ date: Date; hour: number } | null>(null)
  const [activeTimeBlock, setActiveTimeBlock] = useState<any>(null)

  const weekStart = startOfWeek(selectedDate, { weekStartsOn: 0 }) // Sunday start
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i))
  const hours = Array.from({ length: 24 }, (_, i) => i)

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  )

  const handlePreviousWeek = () => {
    setSelectedDate(subWeeks(selectedDate, 1))
  }

  const handleNextWeek = () => {
    setSelectedDate(addWeeks(selectedDate, 1))
  }

  const handleTimeSlotClick = (date: Date, hour: number) => {
    setSelectedTimeSlot({ date, hour })
    setShowTimeBlockForm(true)
  }

  // Enhanced function to get time blocks for a specific slot, including multi-hour events
  const getTimeBlocksForSlot = (date: Date, hour: number) => {
    return timeBlocks.filter((block) => {
      if (block.isFullDay) return false

      const blockStart = new Date(block.startTime)
      const blockEnd = new Date(block.endTime)
      const slotStart = new Date(date)
      slotStart.setHours(hour, 0, 0, 0)
      const slotEnd = new Date(date)
      slotEnd.setHours(hour + 1, 0, 0, 0)

      // Check if this time slot overlaps with the event
      return isSameDay(blockStart, date) &&
             blockStart < slotEnd &&
             blockEnd > slotStart
    })
  }

  // Get the primary time slot for an event (where it should be rendered)
  const getPrimarySlotForEvent = (block: any) => {
    const blockStart = new Date(block.startTime)
    return blockStart.getHours()
  }

  // Check if this is the primary slot for the event (to avoid duplicate rendering)
  const isEventPrimarySlot = (block: any, hour: number) => {
    return getPrimarySlotForEvent(block) === hour
  }

  const getFullDayEventsForDate = (date: Date) => {
    return timeBlocks.filter((block) => {
      return block.isFullDay && isSameDay(new Date(block.startTime), date)
    })
  }

  const handleFullDayClick = (date: Date) => {
    setSelectedTimeSlot({ date, hour: 0 })
    setShowTimeBlockForm(true)
  }

  // Drag and drop handlers
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    const timeBlock = active.data.current?.timeBlock
    if (timeBlock) {
      setActiveTimeBlock(timeBlock)
    }
  }

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event
    setActiveTimeBlock(null)

    if (!over || !active.data.current?.timeBlock) return

    const timeBlock = active.data.current.timeBlock
    const dropData = over.data.current

    if (dropData?.type === 'timeslot') {
      const { date, hour } = dropData

      // Calculate new start and end times
      const duration = new Date(timeBlock.endTime).getTime() - new Date(timeBlock.startTime).getTime()
      const newStartTime = new Date(date)
      newStartTime.setHours(hour, 0, 0, 0)
      const newEndTime = new Date(newStartTime.getTime() + duration)

      // Check if the position actually changed
      const currentStart = new Date(timeBlock.startTime)
      if (currentStart.getTime() === newStartTime.getTime()) {
        return // No change needed
      }

      // Apply optimistic update immediately to move the time block to the new slot
      // This ensures the time block appears in the target slot with loading indicator
      try {
        await updateTimeBlock(timeBlock.id, {
          startTime: newStartTime,
          endTime: newEndTime,
        })
      } catch (error) {
        console.error('Error updating time block:', error)
        // Error handling is done in the hook - will revert to original position
      }
    }
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Calendar Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Week of {format(weekStart, "MMM d, yyyy")}
            </h2>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handlePreviousWeek}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </Button>
              <Button variant="outline" size="sm" onClick={handleNextWeek}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Button>
            </div>
          </div>
          <Button
            onClick={() => setSelectedDate(new Date())}
            variant="outline"
            size="sm"
          >
            Today
          </Button>
        </div>

      {/* Calendar Grid */}
      <div className="overflow-x-auto">
        <div className="min-w-[800px]">
          {/* Day Headers */}
          <div className="grid grid-cols-8 border-b border-gray-200">
            <div className="p-3 text-sm font-medium text-gray-500">Time</div>
            {weekDays.map((day) => (
              <div key={day.toISOString()} className="p-3 text-center border-l border-gray-200">
                <div className="text-sm font-medium text-gray-900">
                  {format(day, "EEE")}
                </div>
                <div className={`text-lg font-semibold mt-1 ${
                  isSameDay(day, new Date()) ? "text-blue-600" : "text-gray-700"
                }`}>
                  {format(day, "d")}
                </div>
              </div>
            ))}
          </div>

          {/* Full Day Events Section */}
          <div className="grid grid-cols-8 border-b-2 border-gray-300 bg-gray-50">
            <div className="p-2 text-xs font-medium text-gray-500 border-r border-gray-200 flex items-center">
              All Day
            </div>
            {weekDays.map((day) => {
              const fullDayEvents = getFullDayEventsForDate(day)
              return (
                <div
                  key={`fullday-${day.toISOString()}`}
                  className="p-1.5 border-l border-gray-200 min-h-[60px] cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleFullDayClick(day)}
                >
                  <div className="space-y-0.5">
                    {fullDayEvents.map((block) => (
                      <TimeBlockItem
                        key={block.id}
                        timeBlock={block}
                        isInCalendarGrid={false}
                      />
                    ))}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Time Slots */}
          <div className="max-h-[600px] overflow-y-auto">
            {hours.map((hour) => (
              <div key={hour} className="grid grid-cols-8 border-b border-gray-100">
                <div className="calendar-time-slot p-3 text-sm text-gray-500 border-r border-gray-200 flex items-center">
                  {format(new Date().setHours(hour, 0, 0, 0), "HH:mm")}
                </div>
                {weekDays.map((day) => {
                  const blocksInSlot = getTimeBlocksForSlot(day, hour)
                  // Only render events in their primary slot to avoid duplicates
                  const primarySlotBlocks = blocksInSlot.filter(block => isEventPrimarySlot(block, hour))



                  return (
                    <DroppableTimeSlot
                      key={`${day.toISOString()}-${hour}`}
                      date={day}
                      hour={hour}
                      onClick={() => handleTimeSlotClick(day, hour)}
                    >
                      {primarySlotBlocks.map((block) => (
                        <TimeBlockItem
                          key={block.id}
                          timeBlock={block}
                          isInCalendarGrid={true}
                          slotHeight={60}
                        />
                      ))}
                    </DroppableTimeSlot>
                  )
                })}
              </div>
            ))}
          </div>
        </div>
      </div>

        {/* Time Block Form Modal */}
        {showTimeBlockForm && (
          <TimeBlockForm
            selectedTimeSlot={selectedTimeSlot}
            onClose={() => {
              setShowTimeBlockForm(false)
              setSelectedTimeSlot(null)
            }}
            onSubmit={() => {
              setShowTimeBlockForm(false)
              setSelectedTimeSlot(null)
            }}
          />
        )}
      </div>

      {/* Drag Overlay */}
      <DragOverlay>
        {activeTimeBlock ? (
          <div className="calendar-drag-overlay">
            <TimeBlockItem
              timeBlock={activeTimeBlock}
              isInCalendarGrid={true}
              slotHeight={60}
            />
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  )
}
