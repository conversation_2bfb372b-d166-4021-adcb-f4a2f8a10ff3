"use client"

import { useState } from "react"
import { format } from "date-fns"
import { useSession } from "next-auth/react"
import { useAppStore, TimeBlock } from "@/lib/store"
import { useTimeBlocks } from "@/hooks/use-timeblocks"
import { Button } from "@/components/ui/button"

interface TimeBlockFormProps {
  timeBlock?: TimeBlock
  selectedTimeSlot?: { date: Date; hour: number } | null
  onClose: () => void
  onSubmit: () => void
}

export function TimeBlockForm({ timeBlock, selectedTimeSlot, onClose, onSubmit }: TimeBlockFormProps) {
  const { data: session } = useSession()
  const { createTimeBlock, updateTimeBlock } = useTimeBlocks()
  const { tasks } = useAppStore()

  const getInitialStartTime = () => {
    if (timeBlock) {
      return format(new Date(timeBlock.startTime), "yyyy-MM-dd'T'HH:mm")
    }
    if (selectedTimeSlot) {
      const date = new Date(selectedTimeSlot.date)
      date.setHours(selectedTimeSlot.hour, 0, 0, 0)
      return format(date, "yyyy-MM-dd'T'HH:mm")
    }
    return format(new Date(), "yyyy-MM-dd'T'HH:mm")
  }

  const getInitialEndTime = () => {
    if (timeBlock) {
      return format(new Date(timeBlock.endTime), "yyyy-MM-dd'T'HH:mm")
    }
    if (selectedTimeSlot) {
      const date = new Date(selectedTimeSlot.date)
      date.setHours(selectedTimeSlot.hour + 1, 0, 0, 0)
      return format(date, "yyyy-MM-dd'T'HH:mm")
    }
    const endTime = new Date()
    endTime.setHours(endTime.getHours() + 1)
    return format(endTime, "yyyy-MM-dd'T'HH:mm")
  }

  const [formData, setFormData] = useState({
    title: timeBlock?.title || "",
    startTime: getInitialStartTime(),
    endTime: getInitialEndTime(),
    taskId: timeBlock?.taskId || "",
    isFullDay: timeBlock?.isFullDay || false,
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.title.trim()) return

    let startTime = new Date(formData.startTime)
    let endTime = new Date(formData.endTime)

    // For full-day events, set times to start and end of day
    if (formData.isFullDay) {
      startTime.setHours(0, 0, 0, 0)
      endTime.setHours(23, 59, 59, 999)
    } else {
      if (endTime <= startTime) {
        alert("End time must be after start time")
        return
      }
    }

    try {
      if (timeBlock) {
        // Update existing time block
        await updateTimeBlock(timeBlock.id, {
          title: formData.title,
          startTime,
          endTime,
          isFullDay: formData.isFullDay,
          taskId: formData.taskId || undefined,
        })
      } else {
        // Create new time block
        await createTimeBlock({
          title: formData.title,
          startTime,
          endTime,
          isFullDay: formData.isFullDay,
          taskId: formData.taskId || undefined,
        })
      }
      onSubmit()
    } catch (error) {
      console.error("Error saving time block:", error)
      alert(error instanceof Error ? error.message : "Failed to save time block")
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            {timeBlock ? "Edit Time Block" : "Create Time Block"}
          </h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-3">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter time block title"
              required
            />
          </div>

          {/* Full Day Checkbox */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isFullDay"
              checked={formData.isFullDay}
              onChange={(e) => setFormData({ ...formData, isFullDay: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isFullDay" className="ml-2 block text-sm text-gray-700">
              All day event
            </label>
          </div>

          {/* Date/Time Inputs */}
          {formData.isFullDay ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date *
              </label>
              <input
                type="date"
                value={formData.startTime.split('T')[0]}
                onChange={(e) => {
                  const date = e.target.value
                  setFormData({
                    ...formData,
                    startTime: `${date}T00:00`,
                    endTime: `${date}T23:59`
                  })
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
          ) : (
            <>
              {/* Start Time */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Time *
                </label>
                <input
                  type="datetime-local"
                  value={formData.startTime}
                  onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              {/* End Time */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Time *
                </label>
                <input
                  type="datetime-local"
                  value={formData.endTime}
                  onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
            </>
          )}

          {/* Associated Task */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Associated Task (Optional)
            </label>
            <select
              value={formData.taskId}
              onChange={(e) => setFormData({ ...formData, taskId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">No associated task</option>
              {tasks.map((task) => (
                <option key={task.id} value={task.id}>
                  {task.title}
                </option>
              ))}
            </select>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-3">
            <Button type="button" variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" className="flex-1">
              {timeBlock ? "Update Time Block" : "Create Time Block"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
