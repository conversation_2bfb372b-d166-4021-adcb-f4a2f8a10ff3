import { renderHook, act } from '@testing-library/react'
import { useSession } from 'next-auth/react'
import { useTimeBlocks } from '@/hooks/use-timeblocks'
import { useAppStore } from '@/lib/store'

// Mock the dependencies
jest.mock('next-auth/react')
jest.mock('@/lib/store')

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>
const mockUseAppStore = useAppStore as jest.MockedFunction<typeof useAppStore>

// Mock fetch
global.fetch = jest.fn()

describe('Optimistic Updates', () => {
  let mockStore: any

  beforeEach(() => {
    mockStore = {
      timeBlocks: [],
      setTimeBlocks: jest.fn(),
      addTimeBlock: jest.fn(),
      updateTimeBlock: jest.fn(),
      deleteTimeBlock: jest.fn(),
      addTimeBlockOptimistic: jest.fn(),
      updateTimeBlockOptimistic: jest.fn(),
      deleteTimeBlockOptimistic: jest.fn(),
      revertTimeBlockOptimistic: jest.fn(),
      confirmTimeBlockOptimistic: jest.fn(),
      addError: jest.fn(),
      removeError: jest.fn(),
      addPendingOperation: jest.fn(),
      removePendingOperation: jest.fn(),
      setIsLoading: jest.fn(),
    }

    mockUseAppStore.mockReturnValue(mockStore)

    mockUseSession.mockReturnValue({
      data: {
        user: { id: '1', name: 'Test User', email: '<EMAIL>' },
      },
      status: 'authenticated',
    } as any)

    // Reset fetch mock
    ;(global.fetch as jest.Mock).mockClear()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('createTimeBlock', () => {
    it('should apply optimistic update immediately', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          id: 'server-id',
          title: 'Test Event',
          startTime: new Date('2024-01-15T09:00:00'),
          endTime: new Date('2024-01-15T10:00:00'),
          isFullDay: false,
          userId: '1',
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      })

      const { result } = renderHook(() => useTimeBlocks())

      const timeBlockData = {
        title: 'Test Event',
        startTime: new Date('2024-01-15T09:00:00'),
        endTime: new Date('2024-01-15T10:00:00'),
        isFullDay: false,
      }

      await act(async () => {
        await result.current.createTimeBlock(timeBlockData)
      })

      // Should add optimistic update immediately
      expect(mockStore.addTimeBlockOptimistic).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Event',
          _optimistic: true,
          _pendingOperation: 'create',
        })
      )

      // Should add pending operation
      expect(mockStore.addPendingOperation).toHaveBeenCalled()

      // Should remove error if any
      expect(mockStore.removeError).toHaveBeenCalled()
    })

    it('should revert optimistic update on error', async () => {
      ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

      const { result } = renderHook(() => useTimeBlocks())

      const timeBlockData = {
        title: 'Test Event',
        startTime: new Date('2024-01-15T09:00:00'),
        endTime: new Date('2024-01-15T10:00:00'),
        isFullDay: false,
      }

      await act(async () => {
        try {
          await result.current.createTimeBlock(timeBlockData)
        } catch (error) {
          // Expected to throw
        }
      })

      // Should revert optimistic update
      expect(mockStore.revertTimeBlockOptimistic).toHaveBeenCalled()

      // Should remove pending operation
      expect(mockStore.removePendingOperation).toHaveBeenCalled()

      // Should add error
      expect(mockStore.addError).toHaveBeenCalledWith(
        expect.any(String),
        'Network error'
      )
    })
  })

  describe('updateTimeBlock', () => {
    it('should apply optimistic update immediately', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          id: '1',
          title: 'Updated Event',
          startTime: new Date('2024-01-15T10:00:00'),
          endTime: new Date('2024-01-15T11:00:00'),
          isFullDay: false,
          userId: '1',
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      })

      const { result } = renderHook(() => useTimeBlocks())

      const updates = {
        title: 'Updated Event',
        startTime: new Date('2024-01-15T10:00:00'),
      }

      await act(async () => {
        await result.current.updateTimeBlock('1', updates)
      })

      // Should apply optimistic update immediately
      expect(mockStore.updateTimeBlockOptimistic).toHaveBeenCalledWith(
        '1',
        expect.objectContaining({
          title: 'Updated Event',
          updatedAt: expect.any(Date),
        })
      )

      // Should confirm optimistic update with server data
      expect(mockStore.confirmTimeBlockOptimistic).toHaveBeenCalledWith(
        '1',
        expect.objectContaining({
          title: 'Updated Event',
        })
      )
    })

    it('should revert optimistic update on error', async () => {
      ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Update failed'))

      const { result } = renderHook(() => useTimeBlocks())

      const updates = {
        title: 'Updated Event',
      }

      await act(async () => {
        try {
          await result.current.updateTimeBlock('1', updates)
        } catch (error) {
          // Expected to throw
        }
      })

      // Should revert optimistic update
      expect(mockStore.revertTimeBlockOptimistic).toHaveBeenCalledWith('1')

      // Should add error
      expect(mockStore.addError).toHaveBeenCalledWith(
        'timeblock-1',
        'Update failed'
      )
    })
  })

  describe('deleteTimeBlock', () => {
    it('should apply optimistic deletion immediately', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
      })

      const { result } = renderHook(() => useTimeBlocks())

      await act(async () => {
        await result.current.deleteTimeBlock('1')
      })

      // Should apply optimistic deletion immediately
      expect(mockStore.deleteTimeBlockOptimistic).toHaveBeenCalledWith('1')

      // Should remove pending operation after success
      expect(mockStore.removePendingOperation).toHaveBeenCalledWith('1')
    })

    it('should revert optimistic deletion on error', async () => {
      ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Delete failed'))

      const { result } = renderHook(() => useTimeBlocks())

      await act(async () => {
        try {
          await result.current.deleteTimeBlock('1')
        } catch (error) {
          // Expected to throw
        }
      })

      // Should revert optimistic deletion
      expect(mockStore.revertTimeBlockOptimistic).toHaveBeenCalledWith('1')

      // Should add error
      expect(mockStore.addError).toHaveBeenCalledWith(
        'timeblock-1',
        'Delete failed'
      )
    })
  })
})
