import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useSession } from 'next-auth/react'
import { Sidebar } from '@/components/dashboard/sidebar'
import { TaskForm } from '@/components/dashboard/task-form'
import { useAppStore } from '@/lib/store'
import { useTasks } from '@/hooks/use-tasks'

// Mock the dependencies
jest.mock('next-auth/react')
jest.mock('@/hooks/use-tasks')
jest.mock('@/lib/store')

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>
const mockUseTasks = useTasks as jest.MockedFunction<typeof useTasks>
const mockUseAppStore = useAppStore as jest.MockedFunction<typeof useAppStore>

describe('Task Creation Functionality', () => {
  const mockSetShowTaskForm = jest.fn()
  const mockSetEditingTask = jest.fn()
  const mockCreateTask = jest.fn()
  const mockUpdateTask = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseSession.mockReturnValue({
      data: {
        user: { id: 'test-user', name: 'Test User', email: '<EMAIL>' }
      },
      status: 'authenticated'
    } as any)

    mockUseTasks.mockReturnValue({
      tasks: [],
      createTask: mockCreateTask,
      updateTask: mockUpdateTask,
      deleteTask: jest.fn(),
      refetch: jest.fn()
    })

    mockUseAppStore.mockReturnValue({
      sidebarCollapsed: false,
      setSidebarCollapsed: jest.fn(),
      setShowTaskForm: mockSetShowTaskForm,
      setEditingTask: mockSetEditingTask,
      showTaskForm: false,
      editingTask: null,
      tasks: [],
      setTasks: jest.fn(),
      addTask: jest.fn(),
      updateTask: jest.fn(),
      deleteTask: jest.fn(),
      timeBlocks: [],
      setTimeBlocks: jest.fn(),
      addTimeBlock: jest.fn(),
      updateTimeBlock: jest.fn(),
      deleteTimeBlock: jest.fn(),
      selectedDate: new Date(),
      setSelectedDate: jest.fn(),
      isLoading: false,
      setIsLoading: jest.fn()
    })
  })

  test('should call setShowTaskForm when Add Task button is clicked', () => {
    render(<Sidebar />)
    
    const addTaskButton = screen.getByText('Add Task')
    fireEvent.click(addTaskButton)
    
    expect(mockSetEditingTask).toHaveBeenCalledWith(null)
    expect(mockSetShowTaskForm).toHaveBeenCalledWith(true)
  })

  test('should render TaskForm when showTaskForm is true', () => {
    mockUseAppStore.mockReturnValue({
      ...mockUseAppStore(),
      showTaskForm: true
    })

    render(<TaskForm onClose={jest.fn()} onSubmit={jest.fn()} />)
    
    expect(screen.getByText('Create New Task')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Enter task title')).toBeInTheDocument()
  })

  test('should create a new task when form is submitted', async () => {
    const mockOnSubmit = jest.fn()
    mockCreateTask.mockResolvedValue({ id: 'new-task', title: 'Test Task' })

    render(<TaskForm onClose={jest.fn()} onSubmit={mockOnSubmit} />)
    
    const titleInput = screen.getByPlaceholderText('Enter task title')
    const submitButton = screen.getByText('Create Task')
    
    fireEvent.change(titleInput, { target: { value: 'Test Task' } })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(mockCreateTask).toHaveBeenCalledWith({
        title: 'Test Task',
        description: '',
        status: 'PENDING',
        priority: 'MEDIUM'
      })
      expect(mockOnSubmit).toHaveBeenCalled()
    })
  })
})
