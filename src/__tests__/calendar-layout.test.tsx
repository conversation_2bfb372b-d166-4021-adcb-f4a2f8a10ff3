import { render, screen } from '@testing-library/react'
import { useSession } from 'next-auth/react'
import { CalendarView } from '@/components/dashboard/calendar-view'
import { useTimeBlocks } from '@/hooks/use-timeblocks'
import { useAppStore } from '@/lib/store'

// Mock the dependencies
jest.mock('next-auth/react')
jest.mock('@/hooks/use-timeblocks')
jest.mock('@/lib/store')

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>
const mockUseTimeBlocks = useTimeBlocks as jest.MockedFunction<typeof useTimeBlocks>
const mockUseAppStore = useAppStore as jest.MockedFunction<typeof useAppStore>

// Mock @dnd-kit/core
jest.mock('@dnd-kit/core', () => ({
  DndContext: ({ children }: { children: React.ReactNode }) => <div data-testid="dnd-context">{children}</div>,
  DragOverlay: ({ children }: { children: React.ReactNode }) => <div data-testid="drag-overlay">{children}</div>,
  useSensor: jest.fn(),
  useSensors: jest.fn(() => []),
  PointerSensor: jest.fn(),
  KeyboardSensor: jest.fn(),
  closestCenter: jest.fn(),
  useDroppable: jest.fn(() => ({
    isOver: false,
    setNodeRef: jest.fn(),
  })),
  useDraggable: jest.fn(() => ({
    attributes: {},
    listeners: {},
    setNodeRef: jest.fn(),
    transform: null,
    isDragging: false,
  })),
}))

describe('Calendar Layout Fixes', () => {
  beforeEach(() => {
    mockUseSession.mockReturnValue({
      data: {
        user: { id: '1', name: 'Test User', email: '<EMAIL>' },
      },
      status: 'authenticated',
    } as any)

    mockUseTimeBlocks.mockReturnValue({
      timeBlocks: [
        {
          id: '1',
          title: 'Test Event',
          startTime: new Date('2024-01-15T09:00:00'),
          endTime: new Date('2024-01-15T10:00:00'),
          isFullDay: false,
          userId: '1',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          title: 'Multi-hour Event',
          startTime: new Date('2024-01-15T14:00:00'),
          endTime: new Date('2024-01-15T16:30:00'),
          isFullDay: false,
          userId: '1',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '3',
          title: 'All Day Event',
          startTime: new Date('2024-01-15T00:00:00'),
          endTime: new Date('2024-01-15T23:59:59'),
          isFullDay: true,
          userId: '1',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      createTimeBlock: jest.fn(),
      updateTimeBlock: jest.fn(),
      deleteTimeBlock: jest.fn(),
      refetch: jest.fn(),
    })

    mockUseAppStore.mockReturnValue({
      selectedDate: new Date('2024-01-15'),
      setSelectedDate: jest.fn(),
      sidebarCollapsed: false,
      setSidebarCollapsed: jest.fn(),
      showTaskForm: false,
      setShowTaskForm: jest.fn(),
      editingTask: null,
      setEditingTask: jest.fn(),
      tasks: [],
      addTask: jest.fn(),
      updateTask: jest.fn(),
      deleteTask: jest.fn(),
      setTasks: jest.fn(),
      timeBlocks: [],
      addTimeBlock: jest.fn(),
      updateTimeBlock: jest.fn(),
      deleteTimeBlock: jest.fn(),
      setTimeBlocks: jest.fn(),
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('renders calendar with fixed row heights', () => {
    render(<CalendarView />)
    
    // Check that DndContext is rendered
    expect(screen.getByTestId('dnd-context')).toBeInTheDocument()
    
    // Check that drag overlay is present
    expect(screen.getByTestId('drag-overlay')).toBeInTheDocument()
    
    // Check that time slots are rendered
    expect(screen.getByText('09:00')).toBeInTheDocument()
    expect(screen.getByText('14:00')).toBeInTheDocument()
    
    // Check that all day section is present
    expect(screen.getByText('All Day')).toBeInTheDocument()
  })

  it('renders events in calendar grid', () => {
    render(<CalendarView />)
    
    // Check that events are rendered
    expect(screen.getByText('Test Event')).toBeInTheDocument()
    expect(screen.getByText('Multi-hour Event')).toBeInTheDocument()
    expect(screen.getByText('All Day Event')).toBeInTheDocument()
  })

  it('applies fixed height CSS classes to time slots', () => {
    const { container } = render(<CalendarView />)
    
    // Check for calendar-time-slot class which applies fixed height
    const timeSlots = container.querySelectorAll('.calendar-time-slot')
    expect(timeSlots.length).toBeGreaterThan(0)
  })

  it('renders week navigation', () => {
    render(<CalendarView />)
    
    // Check for navigation buttons
    expect(screen.getByText('Today')).toBeInTheDocument()
    
    // Check for week header
    expect(screen.getByText(/Week of/)).toBeInTheDocument()
  })
})
