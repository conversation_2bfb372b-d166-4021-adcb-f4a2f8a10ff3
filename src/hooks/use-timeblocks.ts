import { useEffect, useCallback } from "react"
import { useSession } from "next-auth/react"
import { useAppStore, TimeBlock } from "@/lib/store"
import { supabase } from "@/lib/supabase"

// Generate temporary ID for optimistic updates
const generateTempId = () => `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

export function useTimeBlocks() {
  const { data: session } = useSession()
  const {
    timeBlocks,
    setTimeBlocks,
    addTimeBlock,
    updateTimeBlock,
    deleteTimeBlock,
    setIsLoading,
    addTimeBlockOptimistic,
    updateTimeBlockOptimistic,
    deleteTimeBlockOptimistic,
    revertTimeBlockOptimistic,
    confirmTimeBlockOptimistic,
    addError,
    removeError,
    addPendingOperation,
    removePendingOperation
  } = useAppStore()

  const fetchTimeBlocks = async () => {
    if (!session) return

    setIsLoading(true)
    try {
      const response = await fetch("/api/timeblocks")
      if (response.ok) {
        const data = await response.json()
        setTimeBlocks(data)
      }
    } catch (error) {
      console.error("Error fetching time blocks:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const createTimeBlock = async (timeBlockData: Omit<TimeBlock, "id" | "userId" | "createdAt" | "updatedAt">) => {
    const tempId = generateTempId()
    const optimisticTimeBlock: TimeBlock = {
      ...timeBlockData,
      id: tempId,
      userId: session?.user?.id || '',
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Optimistic update - add immediately to UI
    addTimeBlockOptimistic(optimisticTimeBlock)
    addPendingOperation(tempId)
    removeError(`timeblock-${tempId}`)

    try {
      const response = await fetch("/api/timeblocks", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(timeBlockData),
      })

      if (response.ok) {
        const serverTimeBlock = await response.json()

        // Replace optimistic data with server data
        deleteTimeBlock(tempId) // Remove temp
        addTimeBlock(serverTimeBlock) // Add real
        removePendingOperation(tempId)

        return serverTimeBlock
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to create time block")
      }
    } catch (error) {
      console.error("Error creating time block:", error)

      // Revert optimistic update
      revertTimeBlockOptimistic(tempId)
      removePendingOperation(tempId)
      addError(`timeblock-${tempId}`, error instanceof Error ? error.message : "Failed to create time block")

      throw error
    }
  }

  const updateTimeBlockById = async (id: string, updates: Partial<TimeBlock>) => {
    // Optimistic update - apply changes immediately
    updateTimeBlockOptimistic(id, { ...updates, updatedAt: new Date() })
    addPendingOperation(id)
    removeError(`timeblock-${id}`)

    try {
      const response = await fetch(`/api/timeblocks/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updates),
      })

      if (response.ok) {
        const serverTimeBlock = await response.json()

        // Confirm optimistic update with server data
        confirmTimeBlockOptimistic(id, serverTimeBlock)
        removePendingOperation(id)

        return serverTimeBlock
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to update time block")
      }
    } catch (error) {
      console.error("Error updating time block:", error)

      // Revert optimistic update
      revertTimeBlockOptimistic(id)
      removePendingOperation(id)
      addError(`timeblock-${id}`, error instanceof Error ? error.message : "Failed to update time block")

      throw error
    }
  }

  const deleteTimeBlockById = async (id: string) => {
    // Optimistic update - remove immediately from UI
    deleteTimeBlockOptimistic(id)
    addPendingOperation(id)
    removeError(`timeblock-${id}`)

    try {
      const response = await fetch(`/api/timeblocks/${id}`, {
        method: "DELETE",
      })

      if (response.ok) {
        // Confirm deletion
        removePendingOperation(id)
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to delete time block")
      }
    } catch (error) {
      console.error("Error deleting time block:", error)

      // Revert optimistic deletion
      revertTimeBlockOptimistic(id)
      removePendingOperation(id)
      addError(`timeblock-${id}`, error instanceof Error ? error.message : "Failed to delete time block")

      throw error
    }
  }

  // Real-time subscription for timeblocks
  const subscribeToTimeBlocks = useCallback(() => {
    if (!session?.user?.id) return

    const channel = supabase
      .channel('timeblocks-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'TimeBlock',
          filter: `userId=eq.${session.user.id}`,
        },
        (payload) => {
          console.log('TimeBlock change received:', payload)

          switch (payload.eventType) {
            case 'INSERT':
              if (payload.new) {
                const newTimeBlock = {
                  ...payload.new,
                  startTime: new Date(payload.new.startTime),
                  endTime: new Date(payload.new.endTime),
                  createdAt: new Date(payload.new.createdAt),
                  updatedAt: new Date(payload.new.updatedAt),
                } as TimeBlock

                // Check if this is a conflict with an optimistic update
                const existingBlock = timeBlocks.find(b => b.id === newTimeBlock.id)
                if (!existingBlock || !existingBlock._optimistic) {
                  addTimeBlock(newTimeBlock)
                }
              }
              break
            case 'UPDATE':
              if (payload.new) {
                const updatedTimeBlock = {
                  ...payload.new,
                  startTime: new Date(payload.new.startTime),
                  endTime: new Date(payload.new.endTime),
                  createdAt: new Date(payload.new.createdAt),
                  updatedAt: new Date(payload.new.updatedAt),
                } as TimeBlock

                // Check if this conflicts with an optimistic update
                const existingBlock = timeBlocks.find(b => b.id === updatedTimeBlock.id)
                if (existingBlock?._optimistic && existingBlock._pendingOperation === 'update') {
                  // Conflict resolution: server wins, but preserve optimistic changes if they're newer
                  const serverTime = new Date(updatedTimeBlock.updatedAt).getTime()
                  const optimisticTime = new Date(existingBlock.updatedAt).getTime()

                  if (serverTime > optimisticTime) {
                    // Server data is newer, use it
                    confirmTimeBlockOptimistic(updatedTimeBlock.id, updatedTimeBlock)
                  }
                  // Otherwise, keep optimistic changes
                } else {
                  updateTimeBlock(payload.new.id, updatedTimeBlock)
                }
              }
              break
            case 'DELETE':
              if (payload.old) {
                // Check if this conflicts with an optimistic update
                const existingBlock = timeBlocks.find(b => b.id === payload.old.id)
                if (!existingBlock?._optimistic) {
                  deleteTimeBlock(payload.old.id)
                }
              }
              break
          }
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [session?.user?.id, addTimeBlock, updateTimeBlock, deleteTimeBlock])

  useEffect(() => {
    if (session) {
      fetchTimeBlocks()
      const unsubscribe = subscribeToTimeBlocks()
      return unsubscribe
    }
  }, [session, subscribeToTimeBlocks])

  return {
    timeBlocks,
    createTimeBlock,
    updateTimeBlock: updateTimeBlockById,
    deleteTimeBlock: deleteTimeBlockById,
    refetch: fetchTimeBlocks,
  }
}
