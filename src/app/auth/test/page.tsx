"use client"

import { useSession, signIn, signOut } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function AuthTest() {
  const { data: session, status } = useSession()

  const handleSignIn = async () => {
    try {
      const result = await signIn("google", {
        callbackUrl: "/dashboard",
        redirect: false,
      })
      console.log("Sign in result:", result)
    } catch (error) {
      console.error("Sign in error:", error)
    }
  }

  const handleSignOut = async () => {
    try {
      await signOut({ callbackUrl: "/auth/signin" })
    } catch (error) {
      console.error("Sign out error:", error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Authentication Test</h1>
          <p className="text-gray-600 mt-2">Test the authentication flow and debug issues</p>
        </div>

        {/* Session Status */}
        <Card>
          <CardHeader>
            <CardTitle>Session Status</CardTitle>
            <CardDescription>Current authentication state</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <span className="font-medium">Status:</span>
                <Badge variant={status === "authenticated" ? "default" : status === "loading" ? "secondary" : "destructive"}>
                  {status}
                </Badge>
              </div>
              
              {session && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">User:</span>
                    <span>{session.user?.name || "No name"}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Email:</span>
                    <span>{session.user?.email || "No email"}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">User ID:</span>
                    <span className="text-sm text-gray-600">{session.user?.id || "No ID"}</span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Authentication Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Authentication Actions</CardTitle>
            <CardDescription>Test sign in and sign out functionality</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {!session ? (
                <div className="space-y-2">
                  <Button onClick={handleSignIn} className="w-full">
                    Sign In with Google
                  </Button>
                  <Button 
                    onClick={() => signIn("google", { callbackUrl: "/dashboard" })} 
                    variant="outline" 
                    className="w-full"
                  >
                    Sign In with Redirect
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  <Button onClick={handleSignOut} variant="destructive" className="w-full">
                    Sign Out
                  </Button>
                  <Button 
                    onClick={() => window.location.href = "/dashboard"} 
                    className="w-full"
                  >
                    Go to Dashboard
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Debug Information */}
        <Card>
          <CardHeader>
            <CardTitle>Debug Information</CardTitle>
            <CardDescription>Technical details for troubleshooting</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Current URL:</span>
                <span className="ml-2 text-gray-600">{typeof window !== "undefined" ? window.location.href : "N/A"}</span>
              </div>
              <div>
                <span className="font-medium">Expected Callback URL:</span>
                <span className="ml-2 text-gray-600">http://localhost:3001/api/auth/callback/google</span>
              </div>
              <div>
                <span className="font-medium">Session Object:</span>
                <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify(session, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
