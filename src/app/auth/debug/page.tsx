"use client"

import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface DebugInfo {
  environment: {
    nodeEnv: string
    nextauthUrl: string
    hasGoogleClientId: boolean
    hasGoogleClientSecret: boolean
    hasSupabaseUrl: boolean
    hasSupabaseKey: boolean
  }
  database: {
    connectionStatus: string
    error?: string
  }
  session: any
}

export default function AuthDebug() {
  const { data: session, status } = useSession()
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDebugInfo = async () => {
      try {
        const response = await fetch('/api/auth/debug')
        const data = await response.json()
        setDebugInfo(data)
      } catch (error) {
        console.error('Failed to fetch debug info:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDebugInfo()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Authentication Debug</h1>
          <p className="text-gray-600 mt-2">Diagnostic information for troubleshooting auth issues</p>
        </div>

        {/* Session Status */}
        <Card>
          <CardHeader>
            <CardTitle>Session Status</CardTitle>
            <CardDescription>Current authentication session information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="font-medium">Status:</span>
                <Badge variant={status === "authenticated" ? "default" : status === "loading" ? "secondary" : "destructive"}>
                  {status}
                </Badge>
              </div>
              {session && (
                <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                  <pre className="text-sm overflow-auto">
                    {JSON.stringify(session, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Environment Variables */}
        {debugInfo && (
          <Card>
            <CardHeader>
              <CardTitle>Environment Configuration</CardTitle>
              <CardDescription>Check if all required environment variables are set</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">NODE_ENV:</span>
                    <Badge variant="outline">{debugInfo.environment.nodeEnv}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">NEXTAUTH_URL:</span>
                    <Badge variant="outline">{debugInfo.environment.nextauthUrl}</Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Google Client ID:</span>
                    <Badge variant={debugInfo.environment.hasGoogleClientId ? "default" : "destructive"}>
                      {debugInfo.environment.hasGoogleClientId ? "Set" : "Missing"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Google Client Secret:</span>
                    <Badge variant={debugInfo.environment.hasGoogleClientSecret ? "default" : "destructive"}>
                      {debugInfo.environment.hasGoogleClientSecret ? "Set" : "Missing"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Supabase URL:</span>
                    <Badge variant={debugInfo.environment.hasSupabaseUrl ? "default" : "destructive"}>
                      {debugInfo.environment.hasSupabaseUrl ? "Set" : "Missing"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Supabase Key:</span>
                    <Badge variant={debugInfo.environment.hasSupabaseKey ? "default" : "destructive"}>
                      {debugInfo.environment.hasSupabaseKey ? "Set" : "Missing"}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Database Status */}
        {debugInfo && (
          <Card>
            <CardHeader>
              <CardTitle>Database Connection</CardTitle>
              <CardDescription>Database connectivity and configuration status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Connection Status:</span>
                  <Badge variant={debugInfo.database.connectionStatus === "connected" ? "default" : "destructive"}>
                    {debugInfo.database.connectionStatus}
                  </Badge>
                </div>
                {debugInfo.database.error && (
                  <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-800 font-medium">Database Error:</p>
                    <p className="text-red-700 text-sm mt-1">{debugInfo.database.error}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Troubleshooting Guide */}
        <Card>
          <CardHeader>
            <CardTitle>Troubleshooting Guide</CardTitle>
            <CardDescription>Common issues and solutions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-red-600">❌ "Callback" Error in URL</h4>
                <p className="text-sm text-gray-600 mt-1">
                  This usually means the database connection failed during OAuth callback.
                  Check your DATABASE_URL and ensure Supabase is accessible.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-red-600">❌ Database Connection Failed</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Update your DATABASE_URL in .env.local with the correct Supabase credentials.
                  Run: npm run setup-db
                </p>
              </div>
              <div>
                <h4 className="font-medium text-green-600">✅ Everything Working</h4>
                <p className="text-sm text-gray-600 mt-1">
                  If all checks pass, try signing out and signing in again.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
