import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const timeBlocks = await prisma.timeBlock.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        task: true,
      },
      orderBy: {
        startTime: "asc",
      },
    })

    return NextResponse.json(timeBlocks)
  } catch (error) {
    console.error("Error fetching time blocks:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { title, startTime, endTime, taskId, isFullDay } = body

    if (!title || !startTime || !endTime) {
      return NextResponse.json({ error: "Title, start time, and end time are required" }, { status: 400 })
    }

    const start = new Date(startTime)
    const end = new Date(endTime)

    if (end <= start) {
      return NextResponse.json({ error: "End time must be after start time" }, { status: 400 })
    }

    // Check for overlapping time blocks
    const overlapping = await prisma.timeBlock.findFirst({
      where: {
        userId: session.user.id,
        OR: [
          {
            AND: [
              { startTime: { lte: start } },
              { endTime: { gt: start } },
            ],
          },
          {
            AND: [
              { startTime: { lt: end } },
              { endTime: { gte: end } },
            ],
          },
          {
            AND: [
              { startTime: { gte: start } },
              { endTime: { lte: end } },
            ],
          },
        ],
      },
    })

    if (overlapping) {
      return NextResponse.json({ error: "Time block overlaps with existing block" }, { status: 400 })
    }

    const timeBlock = await prisma.timeBlock.create({
      data: {
        title,
        startTime: start,
        endTime: end,
        isFullDay: isFullDay || false,
        userId: session.user.id,
        taskId: taskId || null,
      },
      include: {
        task: true,
      },
    })

    return NextResponse.json(timeBlock, { status: 201 })
  } catch (error) {
    console.error("Error creating time block:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
