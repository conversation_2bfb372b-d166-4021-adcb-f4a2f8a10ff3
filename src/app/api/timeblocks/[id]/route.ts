import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { title, startTime, endTime, taskId, isFullDay } = body

    // Verify the time block belongs to the user
    const existingTimeBlock = await prisma.timeBlock.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    })

    if (!existingTimeBlock) {
      return NextResponse.json({ error: "Time block not found" }, { status: 404 })
    }

    const start = new Date(startTime)
    const end = new Date(endTime)

    if (end <= start) {
      return NextResponse.json({ error: "End time must be after start time" }, { status: 400 })
    }

    // Check for overlapping time blocks (excluding the current one)
    const overlapping = await prisma.timeBlock.findFirst({
      where: {
        userId: session.user.id,
        id: { not: params.id },
        OR: [
          {
            AND: [
              { startTime: { lte: start } },
              { endTime: { gt: start } },
            ],
          },
          {
            AND: [
              { startTime: { lt: end } },
              { endTime: { gte: end } },
            ],
          },
          {
            AND: [
              { startTime: { gte: start } },
              { endTime: { lte: end } },
            ],
          },
        ],
      },
    })

    if (overlapping) {
      return NextResponse.json({ error: "Time block overlaps with existing block" }, { status: 400 })
    }

    const timeBlock = await prisma.timeBlock.update({
      where: {
        id: params.id,
      },
      data: {
        title,
        startTime: start,
        endTime: end,
        isFullDay: isFullDay || false,
        taskId: taskId || null,
      },
      include: {
        task: true,
      },
    })

    return NextResponse.json(timeBlock)
  } catch (error) {
    console.error("Error updating time block:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Verify the time block belongs to the user
    const existingTimeBlock = await prisma.timeBlock.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    })

    if (!existingTimeBlock) {
      return NextResponse.json({ error: "Time block not found" }, { status: 404 })
    }

    await prisma.timeBlock.delete({
      where: {
        id: params.id,
      },
    })

    return NextResponse.json({ message: "Time block deleted successfully" })
  } catch (error) {
    console.error("Error deleting time block:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
