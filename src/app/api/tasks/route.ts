import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { serverSupabaseService } from "@/lib/supabase-service"
import { handleApiError, createErrorResponse, AuthenticationError, ValidationError } from "@/lib/error-handler"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      throw new AuthenticationError()
    }

    const tasks = await prisma.task.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json(tasks)
  } catch (error) {
    console.error("Task fetch error:", error)
    const appError = handleApiError(error)
    const errorResponse = createErrorResponse(appError)
    return NextResponse.json(errorResponse, { status: appError.statusCode })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      throw new AuthenticationError()
    }

    // Verify user exists in database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      console.error("User not found in database:", session.user.id)
      throw new AuthenticationError("User not found in database")
    }

    const body = await request.json()
    const { title, description, status, priority } = body

    if (!title?.trim()) {
      throw new ValidationError("Title is required")
    }

    const task = await prisma.task.create({
      data: {
        title: title.trim(),
        description: description?.trim() || null,
        status: status || "PENDING",
        priority: priority || "MEDIUM",
        userId: session.user.id,
      },
    })

    return NextResponse.json(task, { status: 201 })
  } catch (error) {
    console.error("Task creation error:", error)
    const appError = handleApiError(error)
    const errorResponse = createErrorResponse(appError)
    return NextResponse.json(errorResponse, { status: appError.statusCode })
  }
}
