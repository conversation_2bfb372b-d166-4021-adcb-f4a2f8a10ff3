import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    // Check if user exists in database
    let userInDb = null
    if (session?.user?.id) {
      userInDb = await prisma.user.findUnique({
        where: { id: session.user.id }
      })
    }
    
    return NextResponse.json({
      session: session ? {
        user: session.user,
        expires: session.expires
      } : null,
      userInDatabase: userInDb ? {
        id: userInDb.id,
        email: userInDb.email,
        name: userInDb.name
      } : null,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error("Debug session error:", error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
