import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    // Test database connection
    await prisma.$connect()
    
    // Try to count users
    const userCount = await prisma.user.count()
    
    return NextResponse.json({ 
      status: "connected", 
      userCount,
      message: "Database connection successful" 
    })
  } catch (error) {
    console.error("Database connection error:", error)
    return NextResponse.json({ 
      status: "error", 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}
