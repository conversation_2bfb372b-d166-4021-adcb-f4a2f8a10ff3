import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    // Check environment variables
    const environment = {
      nodeEnv: process.env.NODE_ENV || "unknown",
      nextauthUrl: process.env.NEXTAUTH_URL || "not set",
      hasGoogleClientId: !!process.env.GOOGLE_CLIENT_ID,
      hasGoogleClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    }

    // Test database connection
    let database = {
      connectionStatus: "unknown",
      error: undefined as string | undefined,
    }

    try {
      // Try to connect to the database
      await prisma.$connect()
      
      // Try a simple query
      await prisma.user.findFirst()
      
      database.connectionStatus = "connected"
    } catch (error: any) {
      database.connectionStatus = "failed"
      database.error = error.message || "Unknown database error"
    } finally {
      await prisma.$disconnect()
    }

    return NextResponse.json({
      environment,
      database,
      timestamp: new Date().toISOString(),
    })
  } catch (error: any) {
    return NextResponse.json(
      {
        error: "Failed to generate debug info",
        message: error.message,
      },
      { status: 500 }
    )
  }
}
