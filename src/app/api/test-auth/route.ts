import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    return NextResponse.json({ 
      authenticated: !!session,
      userId: session?.user?.id || null,
      userEmail: session?.user?.email || null,
      session: session ? "exists" : "null"
    })
  } catch (error) {
    console.error("Auth test error:", error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 })
  }
}
