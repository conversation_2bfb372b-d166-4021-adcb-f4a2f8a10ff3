import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/providers/auth-provider";
import { NotificationContainer } from "@/components/ui/notification";
import { GlobalLoadingIndicator } from "@/components/ui/loading-indicator";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "TimeBlock - Smart Time Management & Productivity",
  description: "Take control of your time with TimeBlock's intelligent time management and productivity tools. Organize your day, boost productivity, and achieve your goals.",
  keywords: ["time management", "productivity", "time blocking", "task management", "scheduling"],
  authors: [{ name: "TimeBlock Team" }],
  openGraph: {
    title: "TimeBlock - Smart Time Management & Productivity",
    description: "Take control of your time with TimeBlock's intelligent time management and productivity tools.",
    url: "https://timeblock.app",
    siteName: "TimeBlock",
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "TimeBlock - Smart Time Management & Productivity",
    description: "Take control of your time with TimeBlock's intelligent time management and productivity tools.",
    creator: "@timeblockapp",
  },
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#111827" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link
          rel="apple-touch-icon"
          href="/apple-icon.png"
          type="image/png"
          sizes="180x180"
        />
      </head>
      <body className={`${inter.variable} font-sans antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100`}>
        <AuthProvider>
          {children}
          <NotificationContainer />
          <GlobalLoadingIndicator />
        </AuthProvider>
      </body>
    </html>
  );
}
