import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface Task {
  id: string
  title: string
  description?: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  userId: string
  createdAt: Date
  updatedAt: Date
}

export interface TimeBlock {
  id: string
  title: string
  startTime: Date
  endTime: Date
  isFullDay?: boolean
  userId: string
  taskId?: string
  task?: Task
  createdAt: Date
  updatedAt: Date
  // Optimistic update tracking
  _optimistic?: boolean
  _pendingOperation?: 'create' | 'update' | 'delete'
  _originalData?: Partial<TimeBlock>
}

interface AppState {
  // Tasks
  tasks: Task[]
  setTasks: (tasks: Task[]) => void
  addTask: (task: Task) => void
  updateTask: (id: string, updates: Partial<Task>) => void
  deleteTask: (id: string) => void

  // Time Blocks
  timeBlocks: TimeBlock[]
  setTimeBlocks: (timeBlocks: TimeBlock[]) => void
  addTimeBlock: (timeBlock: TimeBlock) => void
  updateTimeBlock: (id: string, updates: Partial<TimeBlock>) => void
  deleteTimeBlock: (id: string) => void

  // Optimistic Time Block Operations
  addTimeBlockOptimistic: (timeBlock: TimeBlock) => void
  updateTimeBlockOptimistic: (id: string, updates: Partial<TimeBlock>) => void
  deleteTimeBlockOptimistic: (id: string) => void
  revertTimeBlockOptimistic: (id: string) => void
  confirmTimeBlockOptimistic: (id: string, serverData?: Partial<TimeBlock>) => void

  // UI State
  sidebarCollapsed: boolean
  setSidebarCollapsed: (collapsed: boolean) => void
  selectedDate: Date
  setSelectedDate: (date: Date) => void
  showTaskForm: boolean
  setShowTaskForm: (show: boolean) => void
  editingTask: Task | null
  setEditingTask: (task: Task | null) => void

  // Loading states
  isLoading: boolean
  setIsLoading: (loading: boolean) => void

  // Error handling
  errors: { [key: string]: string }
  addError: (key: string, message: string) => void
  removeError: (key: string) => void
  clearErrors: () => void

  // Background operations
  pendingOperations: Set<string>
  addPendingOperation: (id: string) => void
  removePendingOperation: (id: string) => void
}

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // Tasks
      tasks: [],
      setTasks: (tasks) => set({ tasks }),
      addTask: (task) => set((state) => ({ tasks: [...state.tasks, task] })),
      updateTask: (id, updates) =>
        set((state) => ({
          tasks: state.tasks.map((task) =>
            task.id === id ? { ...task, ...updates } : task
          ),
        })),
      deleteTask: (id) =>
        set((state) => ({
          tasks: state.tasks.filter((task) => task.id !== id),
        })),

      // Time Blocks
      timeBlocks: [],
      setTimeBlocks: (timeBlocks) => set({ timeBlocks }),
      addTimeBlock: (timeBlock) =>
        set((state) => ({ timeBlocks: [...state.timeBlocks, timeBlock] })),
      updateTimeBlock: (id, updates) =>
        set((state) => ({
          timeBlocks: state.timeBlocks.map((block) =>
            block.id === id ? { ...block, ...updates } : block
          ),
        })),
      deleteTimeBlock: (id) =>
        set((state) => ({
          timeBlocks: state.timeBlocks.filter((block) => block.id !== id),
        })),

      // Optimistic Time Block Operations
      addTimeBlockOptimistic: (timeBlock) =>
        set((state) => ({
          timeBlocks: [...state.timeBlocks, { ...timeBlock, _optimistic: true, _pendingOperation: 'create' }],
        })),

      updateTimeBlockOptimistic: (id, updates) =>
        set((state) => ({
          timeBlocks: state.timeBlocks.map((block) =>
            block.id === id
              ? {
                  ...block,
                  ...updates,
                  _optimistic: true,
                  _pendingOperation: 'update',
                  _originalData: block._originalData || { ...block },
                }
              : block
          ),
        })),

      deleteTimeBlockOptimistic: (id) =>
        set((state) => ({
          timeBlocks: state.timeBlocks.map((block) =>
            block.id === id
              ? {
                  ...block,
                  _optimistic: true,
                  _pendingOperation: 'delete',
                  _originalData: { ...block },
                }
              : block
          ).filter((block) => !(block.id === id && block._pendingOperation === 'delete')),
        })),

      revertTimeBlockOptimistic: (id) =>
        set((state) => {
          const block = state.timeBlocks.find((b) => b.id === id)
          if (!block || !block._optimistic) return state

          if (block._pendingOperation === 'create') {
            // Remove optimistically created block
            return {
              timeBlocks: state.timeBlocks.filter((b) => b.id !== id),
            }
          } else if (block._pendingOperation === 'update' && block._originalData) {
            // Revert to original data
            return {
              timeBlocks: state.timeBlocks.map((b) =>
                b.id === id
                  ? { ...block._originalData, id, _optimistic: false, _pendingOperation: undefined, _originalData: undefined }
                  : b
              ),
            }
          } else if (block._pendingOperation === 'delete' && block._originalData) {
            // Restore deleted block
            return {
              timeBlocks: [...state.timeBlocks, { ...block._originalData, id, _optimistic: false, _pendingOperation: undefined, _originalData: undefined }],
            }
          }

          return state
        }),

      confirmTimeBlockOptimistic: (id, serverData) =>
        set((state) => ({
          timeBlocks: state.timeBlocks.map((block) =>
            block.id === id
              ? {
                  ...block,
                  ...serverData,
                  _optimistic: false,
                  _pendingOperation: undefined,
                  _originalData: undefined,
                }
              : block
          ),
        })),

      // UI State
      sidebarCollapsed: false,
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      selectedDate: new Date(),
      setSelectedDate: (date) => set({ selectedDate: date }),
      showTaskForm: false,
      setShowTaskForm: (show) => set({ showTaskForm: show }),
      editingTask: null,
      setEditingTask: (task) => set({ editingTask: task }),

      // Loading states
      isLoading: false,
      setIsLoading: (loading) => set({ isLoading: loading }),

      // Error handling
      errors: {},
      addError: (key, message) =>
        set((state) => ({
          errors: { ...state.errors, [key]: message },
        })),
      removeError: (key) =>
        set((state) => {
          const { [key]: removed, ...rest } = state.errors
          return { errors: rest }
        }),
      clearErrors: () => set({ errors: {} }),

      // Background operations
      pendingOperations: new Set(),
      addPendingOperation: (id) =>
        set((state) => ({
          pendingOperations: new Set([...state.pendingOperations, id]),
        })),
      removePendingOperation: (id) =>
        set((state) => {
          const newSet = new Set(state.pendingOperations)
          newSet.delete(id)
          return { pendingOperations: newSet }
        }),
    }),
    {
      name: 'timeblock-store',
    }
  )
)
