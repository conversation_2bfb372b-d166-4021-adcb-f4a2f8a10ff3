import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface Task {
  id: string
  title: string
  description?: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  userId: string
  createdAt: Date
  updatedAt: Date
}

export interface TimeBlock {
  id: string
  title: string
  startTime: Date
  endTime: Date
  isFullDay?: boolean
  userId: string
  taskId?: string
  task?: Task
  createdAt: Date
  updatedAt: Date
}

interface AppState {
  // Tasks
  tasks: Task[]
  setTasks: (tasks: Task[]) => void
  addTask: (task: Task) => void
  updateTask: (id: string, updates: Partial<Task>) => void
  deleteTask: (id: string) => void

  // Time Blocks
  timeBlocks: TimeBlock[]
  setTimeBlocks: (timeBlocks: TimeBlock[]) => void
  addTimeBlock: (timeBlock: TimeBlock) => void
  updateTimeBlock: (id: string, updates: Partial<TimeBlock>) => void
  deleteTimeBlock: (id: string) => void

  // UI State
  sidebarCollapsed: boolean
  setSidebarCollapsed: (collapsed: boolean) => void
  selectedDate: Date
  setSelectedDate: (date: Date) => void
  showTaskForm: boolean
  setShowTaskForm: (show: boolean) => void
  editingTask: Task | null
  setEditingTask: (task: Task | null) => void

  // Loading states
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
}

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // Tasks
      tasks: [],
      setTasks: (tasks) => set({ tasks }),
      addTask: (task) => set((state) => ({ tasks: [...state.tasks, task] })),
      updateTask: (id, updates) =>
        set((state) => ({
          tasks: state.tasks.map((task) =>
            task.id === id ? { ...task, ...updates } : task
          ),
        })),
      deleteTask: (id) =>
        set((state) => ({
          tasks: state.tasks.filter((task) => task.id !== id),
        })),

      // Time Blocks
      timeBlocks: [],
      setTimeBlocks: (timeBlocks) => set({ timeBlocks }),
      addTimeBlock: (timeBlock) =>
        set((state) => ({ timeBlocks: [...state.timeBlocks, timeBlock] })),
      updateTimeBlock: (id, updates) =>
        set((state) => ({
          timeBlocks: state.timeBlocks.map((block) =>
            block.id === id ? { ...block, ...updates } : block
          ),
        })),
      deleteTimeBlock: (id) =>
        set((state) => ({
          timeBlocks: state.timeBlocks.filter((block) => block.id !== id),
        })),

      // UI State
      sidebarCollapsed: false,
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      selectedDate: new Date(),
      setSelectedDate: (date) => set({ selectedDate: date }),
      showTaskForm: false,
      setShowTaskForm: (show) => set({ showTaskForm: show }),
      editingTask: null,
      setEditingTask: (task) => set({ editingTask: task }),

      // Loading states
      isLoading: false,
      setIsLoading: (loading) => set({ isLoading: loading }),
    }),
    {
      name: 'timeblock-store',
    }
  )
)
