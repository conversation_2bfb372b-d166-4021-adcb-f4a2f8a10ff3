import { PostgrestError } from '@supabase/supabase-js'

export interface AppError {
  message: string
  code?: string
  statusCode: number
  details?: any
}

export class DatabaseError extends Error implements AppError {
  statusCode: number
  code?: string
  details?: any

  constructor(message: string, statusCode = 500, code?: string, details?: any) {
    super(message)
    this.name = 'DatabaseError'
    this.statusCode = statusCode
    this.code = code
    this.details = details
  }
}

export class ValidationError extends Error implements AppError {
  statusCode = 400

  constructor(message: string, public details?: any) {
    super(message)
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends Error implements AppError {
  statusCode = 401

  constructor(message = 'Authentication required') {
    super(message)
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends Error implements AppError {
  statusCode = 403

  constructor(message = 'Access denied') {
    super(message)
    this.name = 'AuthorizationError'
  }
}

export class NotFoundError extends Error implements AppError {
  statusCode = 404

  constructor(message = 'Resource not found') {
    super(message)
    this.name = 'NotFoundError'
  }
}

export function handleSupabaseError(error: PostgrestError): DatabaseError {
  console.error('Supabase error:', error)

  // Map common Supabase error codes to user-friendly messages
  switch (error.code) {
    case 'PGRST116':
      return new NotFoundError('The requested resource was not found')

    case 'PGRST301':
      return new ValidationError('Invalid data provided', error.details)

    case '23505': // Unique constraint violation
      return new ValidationError('A record with this information already exists')

    case '23503': // Foreign key constraint violation
      return new ValidationError('Referenced record does not exist')

    case '23502': // Not null constraint violation
      return new ValidationError('Required field is missing')

    case '42501': // Insufficient privilege
      return new AuthorizationError('You do not have permission to perform this action')

    case 'PGRST103': // Ambiguous or missing relationship
      return new DatabaseError('Database relationship error', 500, error.code)

    default:
      return new DatabaseError(
        error.message || 'An unexpected database error occurred',
        500,
        error.code,
        error.details
      )
  }
}

export function handleApiError(error: unknown): AppError {
  if (error instanceof DatabaseError ||
      error instanceof ValidationError ||
      error instanceof AuthenticationError ||
      error instanceof AuthorizationError ||
      error instanceof NotFoundError) {
    return error
  }

  if (error && typeof error === 'object' && 'code' in error) {
    return handleSupabaseError(error as PostgrestError)
  }

  if (error instanceof Error) {
    return new DatabaseError(error.message)
  }

  return new DatabaseError('An unexpected error occurred')
}

export function createErrorResponse(error: AppError) {
  return {
    error: error.message,
    code: error.code,
    details: error.details,
    statusCode: error.statusCode
  }
}

// Utility function for API routes
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args)
    } catch (error) {
      const appError = handleApiError(error)
      throw appError
    }
  }
}

// Client-side error handler for hooks
export function handleClientError(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }

  if (typeof error === 'string') {
    return error
  }

  return 'An unexpected error occurred'
}

// Retry mechanism for transient errors
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> {
  let lastError: unknown

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error

      // Don't retry on client errors (4xx)
      if ((error instanceof DatabaseError ||
           error instanceof ValidationError ||
           error instanceof AuthenticationError ||
           error instanceof AuthorizationError ||
           error instanceof NotFoundError) &&
          error.statusCode >= 400 && error.statusCode < 500) {
        throw error
      }

      if (attempt === maxRetries) {
        break
      }

      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)))
    }
  }

  throw lastError
}

// Connection health checker
export class ConnectionHealthChecker {
  private isHealthy = true
  private lastCheck = 0
  private checkInterval = 30000 // 30 seconds

  async checkHealth(healthCheckFn: () => Promise<boolean>): Promise<boolean> {
    const now = Date.now()

    if (now - this.lastCheck < this.checkInterval) {
      return this.isHealthy
    }

    try {
      this.isHealthy = await healthCheckFn()
      this.lastCheck = now
    } catch (error) {
      console.error('Health check failed:', error)
      this.isHealthy = false
    }

    return this.isHealthy
  }

  getStatus() {
    return {
      isHealthy: this.isHealthy,
      lastCheck: new Date(this.lastCheck),
      nextCheck: new Date(this.lastCheck + this.checkInterval)
    }
  }
}

export const connectionHealthChecker = new ConnectionHealthChecker()
