#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

async function checkDatabase() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking database state...\n');
    
    // Check users
    const users = await prisma.user.findMany();
    console.log(`👥 Users in database: ${users.length}`);
    users.forEach(user => {
      console.log(`  - ID: ${user.id}, Email: ${user.email}, Name: ${user.name}`);
    });
    
    // Check accounts
    const accounts = await prisma.account.findMany();
    console.log(`\n🔗 Accounts in database: ${accounts.length}`);
    accounts.forEach(account => {
      console.log(`  - Provider: ${account.provider}, UserID: ${account.userId}`);
    });
    
    // Check sessions
    const sessions = await prisma.session.findMany();
    console.log(`\n🎫 Sessions in database: ${sessions.length}`);
    sessions.forEach(session => {
      console.log(`  - UserID: ${session.userId}, Expires: ${session.expires}`);
    });
    
    // Check tasks
    const tasks = await prisma.task.findMany();
    console.log(`\n📋 Tasks in database: ${tasks.length}`);
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
