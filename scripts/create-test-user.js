#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

async function createTestUser() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Creating test user for development...\n');
    
    // Create a test user
    const testUser = await prisma.user.create({
      data: {
        id: 'test-user-123',
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: new Date(),
      }
    });
    
    console.log('✅ Test user created:');
    console.log(`  - ID: ${testUser.id}`);
    console.log(`  - Email: ${testUser.email}`);
    console.log(`  - Name: ${testUser.name}`);
    
    // Create a test task for this user
    const testTask = await prisma.task.create({
      data: {
        title: 'Test Task',
        description: 'This is a test task to verify the database setup',
        status: 'PENDING',
        priority: 'MEDIUM',
        userId: testUser.id,
      }
    });
    
    console.log('\n✅ Test task created:');
    console.log(`  - ID: ${testTask.id}`);
    console.log(`  - Title: ${testTask.title}`);
    console.log(`  - Status: ${testTask.status}`);
    
    console.log('\n🎉 Test data created successfully!');
    console.log('\nNote: This is just for testing. You should sign in with Google OAuth for real usage.');
    
  } catch (error) {
    console.error('❌ Failed to create test user:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
