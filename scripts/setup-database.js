#!/usr/bin/env node

/**
 * Database Setup Script for TimeBlock SaaS
 * 
 * This script helps set up the database connection and run initial migrations.
 * Run this after updating your DATABASE_URL in .env.local
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up TimeBlock SaaS Database...\n');

// Check if .env.local exists
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env.local file not found!');
  console.log('Please create .env.local with your database configuration.');
  process.exit(1);
}

// Read .env.local to check DATABASE_URL
const envContent = fs.readFileSync(envPath, 'utf8');
if (envContent.includes('[YOUR-PASSWORD]')) {
  console.error('❌ Please update your DATABASE_URL in .env.local');
  console.log('Replace [YOUR-PASSWORD] with your actual Supabase database password.');
  console.log('\nTo get your password:');
  console.log('1. Go to your Supabase project dashboard');
  console.log('2. Go to Settings → Database');
  console.log('3. Find your database password or reset it');
  console.log('4. Update the DATABASE_URL in .env.local');
  process.exit(1);
}

try {
  console.log('📦 Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });
  
  console.log('\n🔄 Pushing database schema...');
  execSync('npx prisma db push', { stdio: 'inherit' });
  
  console.log('\n✅ Database setup completed successfully!');
  console.log('\nNext steps:');
  console.log('1. Start your development server: npm run dev');
  console.log('2. Test Google OAuth authentication');
  console.log('3. Check that users can sign in and access the dashboard');
  
} catch (error) {
  console.error('\n❌ Database setup failed!');
  console.error('Error:', error.message);
  console.log('\nTroubleshooting:');
  console.log('1. Verify your DATABASE_URL is correct');
  console.log('2. Check that your Supabase project is running');
  console.log('3. Ensure you have the correct database password');
  console.log('4. Try running: npx prisma db push --force-reset');
  process.exit(1);
}
