#!/usr/bin/env node

/**
 * OAuth Configuration Fix Script
 * 
 * This script helps identify and fix common OAuth configuration issues
 */

const fs = require('fs');
const path = require('path');

function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local file not found');
    return null;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      env[key.trim()] = valueParts.join('=').replace(/^["']|["']$/g, '');
    }
  });

  return env;
}

function checkConfiguration() {
  console.log('🔍 Checking OAuth Configuration...\n');

  const env = loadEnvFile();
  if (!env) return;

  // Check required environment variables
  const requiredVars = [
    'NEXTAUTH_URL',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'DATABASE_URL'
  ];

  console.log('📋 Environment Variables:');
  requiredVars.forEach(varName => {
    const value = env[varName];
    const status = value ? '✅' : '❌';
    console.log(`  ${status} ${varName}: ${value ? (varName.includes('SECRET') ? '[HIDDEN]' : value) : 'NOT SET'}`);
  });

  console.log('\n🌐 OAuth Configuration:');
  
  // Extract port from NEXTAUTH_URL
  const nextAuthUrl = env.NEXTAUTH_URL;
  if (nextAuthUrl) {
    const url = new URL(nextAuthUrl);
    const port = url.port || (url.protocol === 'https:' ? '443' : '80');
    console.log(`  📍 Current NEXTAUTH_URL: ${nextAuthUrl}`);
    console.log(`  🔌 Port: ${port}`);
    
    // Check if port matches common development ports
    if (port === '3000' || port === '3001') {
      console.log(`  ⚠️  Development port detected. Make sure Google OAuth is configured for this port.`);
    }
  }

  console.log('\n🔧 Required Google Cloud Console Configuration:');
  console.log('  1. Go to: https://console.cloud.google.com/apis/credentials');
  console.log(`  2. Edit your OAuth 2.0 Client ID`);
  console.log('  3. Add these Authorized redirect URIs:');
  console.log(`     - ${env.NEXTAUTH_URL}/api/auth/callback/google`);
  console.log(`     - http://localhost:3000/api/auth/callback/google`);
  console.log(`     - http://localhost:3001/api/auth/callback/google`);
  console.log('  4. Add these Authorized JavaScript origins:');
  console.log(`     - ${env.NEXTAUTH_URL}`);
  console.log(`     - http://localhost:3000`);
  console.log(`     - http://localhost:3001`);

  console.log('\n🧪 Testing URLs:');
  console.log(`  🔍 Debug Page: ${env.NEXTAUTH_URL}/auth/debug`);
  console.log(`  🔐 Sign In Page: ${env.NEXTAUTH_URL}/auth/signin`);
  console.log(`  🧪 Test Page: ${env.NEXTAUTH_URL}/auth/test`);
  console.log(`  📊 Dashboard: ${env.NEXTAUTH_URL}/dashboard`);

  console.log('\n📝 Next Steps:');
  console.log('  1. Update Google Cloud Console with the redirect URIs above');
  console.log('  2. Test authentication at the test page');
  console.log('  3. Check browser console for any errors');
  console.log('  4. Verify database connection is working');
}

function fixPortConfiguration() {
  console.log('🔧 Fixing port configuration...\n');

  const env = loadEnvFile();
  if (!env) return;

  // Check if we need to update the port
  const currentUrl = env.NEXTAUTH_URL;
  if (currentUrl && currentUrl.includes(':3000')) {
    console.log('⚠️  NEXTAUTH_URL is set to port 3000, but server might be running on 3001');
    console.log('   Consider updating .env.local if needed');
  }

  // Check if server is running and on which port
  const { exec } = require('child_process');
  exec('lsof -ti:3000,3001', (error, stdout, stderr) => {
    if (stdout) {
      const ports = stdout.trim().split('\n');
      console.log('🔌 Active ports:');
      if (ports.some(p => p)) {
        console.log('   Port 3000 or 3001 is in use');
        console.log('   Make sure NEXTAUTH_URL matches the actual port');
      }
    }
  });
}

// Main execution
console.log('🚀 OAuth Configuration Checker\n');
checkConfiguration();
fixPortConfiguration();

console.log('\n✨ Configuration check complete!');
console.log('   Run this script anytime you need to verify your OAuth setup.');
