#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

async function cleanupTestData() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧹 Cleaning up test data...\n');
    
    // Delete test tasks
    const deletedTasks = await prisma.task.deleteMany({
      where: {
        userId: 'test-user-123'
      }
    });
    
    // Delete test user
    const deletedUser = await prisma.user.deleteMany({
      where: {
        id: 'test-user-123'
      }
    });
    
    console.log(`✅ Deleted ${deletedTasks.count} test tasks`);
    console.log(`✅ Deleted ${deletedUser.count} test users`);
    console.log('\n🎉 Test data cleanup completed!');
    
  } catch (error) {
    console.error('❌ Failed to cleanup test data:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupTestData();
