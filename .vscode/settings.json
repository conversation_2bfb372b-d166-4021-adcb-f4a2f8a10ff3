{"editor.quickSuggestions": {"comments": "on", "strings": "on", "other": "on"}, "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "editor.autoClosingQuotes": "always", "editor.autoClosingBrackets": "always", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "css.validate": false, "scss.validate": false, "less.validate": false, "tailwindCSS.emmetCompletions": true, "tailwindCSS.validate": true, "files.associations": {"*.css": "tailwindcss"}, "editor.inlineSuggest.enabled": true}