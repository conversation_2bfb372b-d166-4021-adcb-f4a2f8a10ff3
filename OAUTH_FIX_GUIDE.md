# Google OAuth Authentication Fix Guide

## 🔍 Problem Identified

The "dashboard&error=Callback" error occurs because:

1. **Database Connection Issue**: <PERSON><PERSON><PERSON> is trying to connect to `localhost:5432` instead of Supabase
2. **OAuth Callback Failure**: When Google redirects back to your app, NextAuth can't store user data due to database connection failure
3. **Mixed Configuration**: The app has both local and Supabase database configurations

## 🛠️ Solution Steps

### Step 1: Update Database Configuration

1. **Get your Supabase database password**:
   - Go to your Supabase project dashboard
   - Navigate to **Settings** → **Database**
   - Find your database password or reset it if needed

2. **Update `.env.local`**:
   ```env
   # Replace [YOUR-PASSWORD] with your actual Supabase database password
   DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.qyozvhyrdtpkpfpuoimu.supabase.co:5432/postgres"
   ```

### Step 2: Set Up Database Schema

Run the database setup script:
```bash
npm run setup-db
```

Or manually:
```bash
npx prisma generate
npx prisma db push
```

### Step 3: Verify Configuration

1. **Check environment variables** by visiting: `http://localhost:3000/auth/debug`
2. **Verify all required variables are set**:
   - ✅ GOOGLE_CLIENT_ID
   - ✅ GOOGLE_CLIENT_SECRET  
   - ✅ NEXTAUTH_URL
   - ✅ DATABASE_URL (pointing to Supabase)
   - ✅ NEXT_PUBLIC_SUPABASE_URL
   - ✅ NEXT_PUBLIC_SUPABASE_ANON_KEY

### Step 4: Configure Google Cloud Console

1. **Go to Google Cloud Console** → **APIs & Services** → **Credentials**
2. **Find your OAuth 2.0 Client ID**: `1009737975616-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com`
3. **Update Authorized redirect URIs**:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://your-domain.com/api/auth/callback/google` (production)

### Step 5: Configure Supabase Authentication

1. **Go to Supabase Dashboard** → **Authentication** → **Providers**
2. **Enable Google provider**
3. **Add your Google OAuth credentials**:
   - Client ID: `1009737975616-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com`
   - Client Secret: `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`
4. **Configure Site URL**: `http://localhost:3000`
5. **Add Redirect URLs**:
   - `http://localhost:3000/api/auth/callback/google`
   - `http://localhost:3000/auth/signin`

## 🧪 Testing the Fix

### 1. Start Development Server
```bash
npm run dev
```

### 2. Test Authentication Flow
1. Go to `http://localhost:3000/auth/signin`
2. Click "Sign in with Google"
3. Complete Google OAuth flow
4. Should redirect to `http://localhost:3000/dashboard` (without error)

### 3. Debug if Issues Persist
- Visit `http://localhost:3000/auth/debug` to check configuration
- Check browser console for errors
- Check terminal logs for database connection issues

## 🔧 Troubleshooting

### Common Issues:

**❌ Database Connection Failed**
- Verify DATABASE_URL has correct password
- Check Supabase project is running
- Run: `npm run setup-db`

**❌ "Configuration" Error**
- Check all environment variables are set
- Verify Google OAuth credentials
- Restart development server

**❌ "AccessDenied" Error**
- Check Google Cloud Console settings
- Verify authorized redirect URIs
- Check Supabase provider configuration

**❌ Still Getting "Callback" Error**
- Clear browser cookies/localStorage
- Check database schema is properly set up
- Verify NextAuth can connect to database

## 📝 Files Modified

1. **`.env.local`** - Updated DATABASE_URL to point to Supabase
2. **`src/lib/auth.ts`** - Added redirect callback and debug mode
3. **`scripts/setup-database.js`** - Database setup automation
4. **`src/app/auth/debug/page.tsx`** - Debug interface
5. **`src/app/api/auth/debug/route.ts`** - Debug API endpoint
6. **`package.json`** - Added database setup scripts

## ✅ Expected Result

After following these steps:
1. Google OAuth should work without "Callback" errors
2. Users should successfully sign in and reach the dashboard
3. User data should be stored in Supabase database
4. Authentication state should persist across sessions

## 🚀 Next Steps

Once authentication is working:
1. Test creating tasks and time blocks
2. Verify real-time updates work
3. Test sign out functionality
4. Set up production environment with proper domain URLs
