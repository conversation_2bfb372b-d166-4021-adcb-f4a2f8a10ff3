{"name": "timeblock-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "setup-db": "node scripts/setup-database.js", "db:push": "prisma db push", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@next/env": "^15.3.3", "@prisma/client": "^6.8.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "14.1.0", "next-auth": "^4.24.11", "prisma": "^6.8.2", "react": "^18.3.1", "react-dom": "^18.3.1", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20.11.30", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.35", "postcss-import": "^16.0.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}